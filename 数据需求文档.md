# 合同AI对运营管理的影响研究-数据需求文档（脱敏版）
本研究旨在通过对比分析AI引入前后的合同质量和项目绩效等数据，系统评估合同AI对企业运营的影响。因此，需要收集合同AI上线前的合同及对应项目落地情况的相关数据，并在后续收集合同AI接入之后的数据。基于实证研究的数据要求，需要收集AI落地前18个月及落地后18个月的数据。

目前阶段，具体数据需求如下：
1. 合同相关数据
1.1 合同基础信息
- 项目编号：用于各表格间数据匹配的唯一标识
- 合同类型：软件开发、系统集成、技术服务等
- 客户类型分类：大型制造企业、中型金融机构、小型服务公司等（按行业和规模分类）
- 签约时间：合同正式签署日期
- 合同期限：合同约定的项目执行周期
- 合同总金额：合同约定的总价值
- 合同状态：执行中、已完成、已终止等
1.2 合同条款内容
- 关键条款覆盖情况：技术规范、交付标准、验收条件、付款条件、违约责任等条款的完备性
- 风险分配条款：技术风险、进度风险、成本风险的责任分配明确性
- 争议解决条款：争议解决机制和程序的约定完整性
- 知识产权条款：知识产权归属和使用权的约定清晰度
- 变更管理条款：项目变更流程和责任的约定情况
1.3 合同复杂度特征
- 条款数量：合同包含的条款总数
- 特殊条款数量：非标准条款的数量
- 复杂度评分：基于项目技术难度和合同条款复杂性的综合评分
- 涉及系统数量：项目涉及的业务系统和技术模块数量
1.4 合同风险相关信息
- AI落地前的合同风险标注信息
- AI落地后的合同风险标注信息
2. 项目管理相关数据
2.1 项目基础信息
- 项目编号：与合同数据匹配的唯一标识
- 项目类型：系统开发、平台建设、数据迁移等
- 客户规模分类：大型企业（员工>1000人）、中型企业（100-1000人）、小型企业（<100人）
- 客户行业分类：制造业、金融业、服务业、政府机构等
- 项目规模：项目团队人数、涉及业务模块数量
2.2 项目时间管理
- 计划开始时间，实际开始时间
- 计划结束时间，实际结束时间
- 计划上线时间，实际上线时间
- 计划验收时间，实际验收时间
- 关键里程碑时间：重要节点的计划时间和实际完成时间
2.3 项目资源管理
- 计划工时：项目计划投入的总工时
- 实际工时：项目实际投入的总工时
- 人力资源配置：各阶段的人员配置情况
- 预算成本：项目初始预算金额
- 实际成本：项目实际发生的成本
2.4 项目质量管理
- 验收结果：项目验收通过情况
- 返工次数：项目实施过程中的返工次数
- 客户满意度：客户对项目交付的满意度评分
- 质量问题记录：项目实施过程中发现的质量问题数量和类型
3. 项目回款相关数据
3.1 付款条款设置
- 付款节点：合同约定的付款时间节点
- 付款比例：各节点的付款比例分配
3.2 回款执行情况
- 计划回款时间：按合同约定的回款时间节点
- 实际回款时间：实际收到款项的时间
- 已收款金额：截至目前已收到的款项
- 应收账款余额：尚未收回的应收账款
- 回款完成率：已回款金额占合同总金额的比例
3.3 回款风险状况
- 回款逾期状态：是否存在回款逾期情况
- 逾期天数：从约定回款日期到实际回款的天数
- 逾期金额：逾期回款的具体金额
- 逾期原因：客户逾期付款的具体原因分类

